import { create } from 'zustand';

export const useStore = create((set) => ({
    selectedSectionName: null,
    sectionCenters: {},
    setSelectedSectionName: (name) => set(state => ({
        selectedSectionName: state.selectedSectionName === name ? null : name
    })),
    setSectionCenters: (centers) => set({ sectionCenters: centers }),
    selectedSubSectionName: null,
    setSelectedSubSectionName: (name) => set({ selectedSubSectionName: name }),

    // Camera transition state
    isCameraTransitioning: false,
    setIsCameraTransitioning: (isTransitioning) => set({ isCameraTransitioning: isTransitioning }),

    progress: 0,
    isLoaded: false,
    shouldStartCameraAnimation: false,
    actions: {
        setProgress: (progress) => set({ progress }),
        setLoaded: (isLoaded) => set({ isLoaded }),
        setShouldStartCameraAnimation: (shouldStart) => set({ shouldStartCameraAnimation: shouldStart }),
    },
}));


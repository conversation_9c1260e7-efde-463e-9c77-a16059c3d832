import React, { useRef, useEffect } from 'react';
import { useThree, useFrame } from '@react-three/fiber';
import { useStore } from '../state/UseStore';
import * as THREE from 'three';

const CameraIntroAnimation = ({ modelCenter }) => {
    const { camera, controls } = useThree();
    const shouldStartCameraAnimation = useStore(state => state.shouldStartCameraAnimation);
    const setShouldStartCameraAnimation = useStore(state => state.actions.setShouldStartCameraAnimation);

    console.log('🎬 CameraIntroAnimation: shouldStartCameraAnimation =', shouldStartCameraAnimation);
    
    const animationRef = useRef({
        isAnimating: false,
        startTime: null,
        duration: 3000, // 3 seconds
        startPosition: new THREE.Vector3(0, 200, 0), // Very high top-down view
        startTarget: new THREE.Vector3(0, 0, 0),
        endPosition: new THREE.Vector3(50, 10, 30), // Default camera position
        endTarget: new THREE.Vector3(0, 0, 0), // Default target
    });

    // Start animation when loading is complete
    useEffect(() => {
        if (shouldStartCameraAnimation && !animationRef.current.isAnimating) {
            console.log('🎬 Starting camera intro animation');
            console.log('🎬 Camera current position:', camera.position);
            console.log('🎬 Controls available:', !!controls);

            animationRef.current.isAnimating = true;
            animationRef.current.startTime = Date.now();

            // Set initial camera position (top-down view)
            camera.position.copy(animationRef.current.startPosition);
            if (controls) {
                controls.target.copy(animationRef.current.startTarget);
                controls.enabled = false; // Disable controls during animation
                controls.update();
            }

            console.log('🎬 Camera set to position:', camera.position);
        }
    }, [shouldStartCameraAnimation, camera, controls]);

    useFrame(() => {
        const anim = animationRef.current;

        if (!anim.isAnimating || !anim.startTime) return;

        const elapsed = Date.now() - anim.startTime;
        const progress = Math.min(elapsed / anim.duration, 1);

        // Debug logging (throttled)
        if (Math.random() < 0.05) { // 5% chance to log
            console.log('🎬 Animation progress:', progress.toFixed(2));
        }

        // Use easeInOutCubic for smooth animation
        const easeProgress = progress < 0.5
            ? 4 * progress * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        // Interpolate camera position
        const currentPosition = new THREE.Vector3().lerpVectors(
            anim.startPosition, 
            anim.endPosition, 
            easeProgress
        );
        
        // Interpolate target position (using model center if available)
        const targetPosition = modelCenter || anim.endTarget;
        const currentTarget = new THREE.Vector3().lerpVectors(
            anim.startTarget,
            targetPosition,
            easeProgress
        );

        // Apply positions
        camera.position.copy(currentPosition);
        if (controls) {
            controls.target.copy(currentTarget);
            controls.update();
        }

        // Animation complete
        if (progress >= 1) {
            console.log('🎬 Camera intro animation complete');
            anim.isAnimating = false;
            anim.startTime = null;
            setShouldStartCameraAnimation(false);

            // Re-enable controls after animation
            if (controls) {
                controls.enabled = true;
            }
        }
    });

    return null;
};

export default CameraIntroAnimation;

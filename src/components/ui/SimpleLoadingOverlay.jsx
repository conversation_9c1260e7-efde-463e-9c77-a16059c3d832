import React, { useState, useEffect, useRef } from 'react';
import { useStore } from '../../state/UseStore';

const SimpleLoadingOverlay = () => {
  const progress = useStore((state) => state.progress);
  const setShouldStartCameraAnimation = useStore(state => state.actions.setShouldStartCameraAnimation);
  const videoRef = useRef(null);
  const [videoEnded, setVideoEnded] = useState(false);
  const [currentLoadingText, setCurrentLoadingText] = useState('Loading assets...');

  const loadingTexts = [
    'Loading assets...',
    'Packing resources...',
    'Initializing systems...',
    'Preparing environment...',
    'Loading models...',
    'Setting up scene...',
    'Optimizing performance...',
    'Almost ready...'
  ];

  // Cycle through loading texts
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentLoadingText(prev => {
        const currentIndex = loadingTexts.indexOf(prev);
        const nextIndex = (currentIndex + 1) % loadingTexts.length;
        return loadingTexts[nextIndex];
      });
    }, 2000); // Change text every 2 seconds

    return () => clearInterval(interval);
  }, []);

  // Auto-play video when component mounts
  useEffect(() => {
    if (videoRef.current) {
      console.log('🎥 SimpleLoading: Attempting to play video');
      videoRef.current.play()
        .then(() => console.log('🎥 SimpleLoading: Video playing successfully'))
        .catch(error => console.error('🎥 SimpleLoading: Video play error:', error));
    }
  }, []);

  // Handle video end and trigger camera animation
  const handleVideoEnd = () => {
    console.log('🎥 SimpleLoading: Video ended');
    setVideoEnded(true);
    setCurrentLoadingText('Starting experience...');
    // Start camera animation when video ends
    setTimeout(() => {
      console.log('🎬 SimpleLoading: Triggering camera animation after video end');
      setShouldStartCameraAnimation(true);
    }, 500);
  };

  // Fallback: If loading takes too long, trigger animation anyway
  useEffect(() => {
    if (progress === 100) {
      const fallbackTimer = setTimeout(() => {
        if (!videoEnded) {
          console.log('🎬 SimpleLoading: Fallback - triggering animation after 10 seconds');
          setShouldStartCameraAnimation(true);
        }
      }, 10000); // 10 second fallback

      return () => clearTimeout(fallbackTimer);
    }
  }, [progress, videoEnded, setShouldStartCameraAnimation]);

  const counter = Math.round(progress);

  return (
    <div className="relative w-full h-full z-[1000] overflow-hidden">
      {/* Video Background */}
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover"
        autoPlay
        muted
        playsInline
        onLoadStart={() => console.log('🎥 SimpleLoading: Video load started')}
        onCanPlay={() => console.log('🎥 SimpleLoading: Video can play')}
        onEnded={handleVideoEnd}
        onError={(e) => console.error('🎥 SimpleLoading: Video error:', e)}
      >
        <source src="/videos/loading.mp4" type="video/mp4" />
      </video>
      
      {/* Dark overlay for better text visibility */}
      <div className="absolute inset-0 bg-black/50"></div>
      
      {/* Simple Loading UI Overlay */}
      <div className="relative bg-transparent text-white flex flex-col items-center justify-center w-full h-full p-4 z-10">
        
        {/* Loading Progress */}
        <div className="text-center mb-8">
          <div className="text-6xl md:text-8xl font-bold text-cyan-400 mb-4">
            {counter}%
          </div>
          
          {/* Progress Bar */}
          <div className="w-80 h-2 bg-gray-800 rounded-full overflow-hidden mb-6">
            <div 
              className="h-full bg-gradient-to-r from-cyan-500 to-blue-500 transition-all duration-300 ease-out"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Loading Text */}
        <div className="text-center">
          <h1 className="text-4xl md:text-5xl font-light text-white mb-4">
            LOADING
          </h1>
          <p className="text-lg md:text-xl text-cyan-300 font-light tracking-wider">
            {currentLoadingText}
          </p>
        </div>

        {/* Optional: Small loading dots animation */}
        <div className="flex space-x-2 mt-8">
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>
    </div>
  );
};

export default SimpleLoadingOverlay;

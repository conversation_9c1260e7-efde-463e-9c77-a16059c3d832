import React, { useEffect } from 'react';
import { useStore } from './state/UseStore';
import FuturisticLoader from './components/ui/Loading';
import SimpleLoadingOverlay from './components/ui/SimpleLoadingOverlay';
import City from './components/City';
import Overlay from './components/Overlay';
import RotateDevice from './components/ui/RotateDevice';
import { Leva } from 'leva';


function App() {
  const isLoaded = useStore(state => state.isLoaded);
  const shouldStartCameraAnimation = useStore(state => state.shouldStartCameraAnimation);

  // Hide loading screen when camera animation starts (video has ended)
  const shouldHideLoader = shouldStartCameraAnimation;

  return (
    <main style={{ position: 'relative', width: '100vw', height: '100vh' }}>
      <Leva hidden={true} />
      <RotateDevice />
      <div style={{ position: 'absolute', inset: 0 }} className='top-0 left-0 overflow-hidden'>
        <City />
        <Overlay />
      </div>
      {!shouldHideLoader && (
        <div style={{ position: 'absolute', inset: 0, zIndex: 100000000 }}>
          <SimpleLoadingOverlay />
        </div>
      )}
    </main>
  );
}

export default App;

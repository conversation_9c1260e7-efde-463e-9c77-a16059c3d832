import React, { useEffect } from 'react';
import { useStore } from './state/UseStore';
import FuturisticLoader from './components/ui/Loading';
import City from './components/City';
import Overlay from './components/Overlay';
import RotateDevice from './components/ui/RotateDevice';
import { Leva } from 'leva';


function App() {
  const isLoaded = useStore(state => state.isLoaded);
  const setShouldStartCameraAnimation = useStore(state => state.actions.setShouldStartCameraAnimation);

  // Trigger camera animation when loading is complete
  useEffect(() => {
    console.log('🔄 App: isLoaded changed to:', isLoaded);
    if (isLoaded) {
      console.log('🎬 App: Triggering camera animation in 500ms');
      // Small delay to ensure everything is rendered
      setTimeout(() => {
        console.log('🎬 App: Setting shouldStartCameraAnimation to true');
        setShouldStartCameraAnimation(true);
      }, 500);
    }
  }, [isLoaded, setShouldStartCameraAnimation]);

  return (
    <main style={{ position: 'relative', width: '100vw', height: '100vh' }}>
      <Leva hidden={true} />
      <RotateDevice />
      <div style={{ position: 'absolute', inset: 0 }} className='top-0 left-0 overflow-hidden'>
        <City />
        <Overlay />
      </div>
      {!isLoaded && (
        <div style={{ position: 'absolute', inset: 0, zIndex: 100000000 }}>
          <FuturisticLoader />
        </div>
      )}
    </main>
  );
}

export default App;
